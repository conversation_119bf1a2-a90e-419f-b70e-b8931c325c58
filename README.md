# UI Element Analyzer

A powerful Python script that analyzes UI elements from web page screenshots using LangChain, LangGraph, vector databases, and Google's Gemini AI model.

## Features

- **Multi-modal Analysis**: Combines screenshot images, coordinate data, and DOM information
- **Vector Database Storage**: Uses ChromaDB for efficient storage and retrieval of UI element data
- **Semantic Search**: Find relevant UI elements based on natural language queries
- **LangGraph Workflow**: Structured processing pipeline for query analysis
- **Gemini AI Integration**: Advanced AI responses with contextual understanding
- **Interactive Web Interface**: Streamlit-based UI for easy interaction

## Requirements

- Python 3.8+
- Google API Key for Gemini model
- Required Python packages (see requirements.txt)

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up Google API Key**:
   - Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Copy `.env.template` to `.env`
   - Add your API key to the `.env` file:
     ```
     GOOGLE_API_KEY=your_actual_api_key_here
     ```

## Usage

### Option 1: Web Interface (Recommended)

Run the Streamlit web application:
```bash
streamlit run streamlit_app.py
```

This will open a web interface where you can:
- Upload your files (screenshot, coordinates JSON, element info JSON)
- Ask questions about UI elements
- Get detailed AI-powered responses

### Option 2: Command Line

Run the main script directly:
```bash
python ui_analyzer.py
```

This will run with the default files and show example queries.

## Input Files

The system requires three input files:

### 1. Screenshot Image
- Format: PNG, JPG, or JPEG
- The actual screenshot of the web page

### 2. Coordinates JSON
Structure:
```json
[
  {
    "coordinates": { "x": 949, "y": 385, "width": 626, "height": 330 },
    "label": "Video"
  },
  {
    "coordinates": { "x": 323, "y": 451, "width": 602, "height": 128 },
    "label": "Main Heading"
  }
]
```

### 3. Element Info JSON
Structure:
```json
{
  "element_1": {
    "attributes": { "class": "video-class", "src": "video.mp4" },
    "classes": ["video-class"],
    "computedStyle": { "width": "626px", "height": "330px" },
    "cssSelector": "video.video-class",
    "tag": "video",
    "text": "",
    "xpath": "//video[1]"
  }
}
```

## Example Queries

- "Where is the brand logo shown on the page?"
- "What is the function of the video element?"
- "Tell me about the main heading and its location"
- "What interactive elements are available in the header?"
- "What are the dimensions of the figure element?"

## How It Works

1. **Data Ingestion**: The system processes the three input files and stores UI element data in a ChromaDB vector database
2. **Query Processing**: User queries are processed through a LangGraph workflow
3. **Semantic Search**: Relevant UI elements are found using vector similarity search
4. **Context Generation**: Contextual information is compiled from coordinates, DOM data, and screenshot reference
5. **AI Response**: Gemini model generates detailed responses based on the context

## Architecture

- **LangChain**: Framework for building AI applications
- **LangGraph**: Workflow orchestration for complex AI processes
- **ChromaDB**: Vector database for storing and searching UI element data
- **Gemini AI**: Google's advanced language model for generating responses
- **Streamlit**: Web interface for user interaction

## Configuration

Modify `config.py` to adjust:
- Model parameters (temperature, max tokens)
- Vector database settings
- File paths
- Search parameters

## Troubleshooting

1. **API Key Issues**: Ensure your Google API key is correctly set in the `.env` file
2. **File Format**: Ensure JSON files are properly formatted
3. **Dependencies**: Install all required packages from `requirements.txt`
4. **Memory**: Large DOM data may require more memory; adjust batch sizes if needed

## License

This project is open source and available under the MIT License.
