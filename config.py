import os
from dotenv import load_dotenv

load_dotenv()

# Configuration settings
class Config:
    # Gemini API configuration
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

    # Hugging Face configuration
    HUGGINGFACE_TOKEN = os.getenv("HUGGINGFACE_TOKEN", "*************************************")

    # ChromaDB configuration
    CHROMA_DB_PATH = "./chroma_db"
    COLLECTION_NAME = "ui_elements"

    # File paths
    DEFAULT_SCREENSHOT_PATH = "notioncom.png"
    DEFAULT_COORDINATES_PATH = "coordinates.json"
    DEFAULT_ELEMENT_INFO_PATH = "element_info.json"
    DEFAULT_LABELS_PATH = "label.txt"

    # Model configuration
    MODEL_NAME = "gemini-2.5-flash-preview-05-20"
    TEMPERATURE = 0.7
    MAX_TOKENS = 10000

    # Vector database configuration
    EMBEDDING_MODEL = "all-MiniLM-L6-v2"
    TOP_K_RESULTS = 3
