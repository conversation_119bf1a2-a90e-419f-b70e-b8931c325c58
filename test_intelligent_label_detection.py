#!/usr/bin/env python3
"""
Test script to demonstrate intelligent label detection that identifies
which specific UI element label the user is asking about from label.txt
"""

import json
from ui_analyzer import UIElementProcessor

def test_intelligent_label_detection():
    """Test the intelligent label detection functionality"""
    
    print("🧠 Testing Intelligent Label Detection")
    print("=" * 60)
    print("This test demonstrates how the system intelligently identifies")
    print("which specific UI element label the user is asking about.")
    print()
    
    # Initialize processor
    processor = UIElementProcessor()
    
    # Load sample data
    processor.process_and_store_data("coordinates.json", "element_info.json")
    
    print(f"📊 System Status:")
    print(f"   Controlled vocabulary terms: {len(processor.controlled_vocabulary)}")
    print(f"   Available UI elements: {len(processor.coordinates_data)}")
    
    # Show available elements in our test data
    print(f"\n📋 Available UI Elements in Test Data:")
    for i, coord in enumerate(processor.coordinates_data):
        print(f"   {i+1}. {coord['label']} at ({coord['coordinates']['x']}, {coord['coordinates']['y']})")
    
    # Test cases for intelligent label detection
    test_cases = [
        {
            "query": "What is the video element on the page?",
            "expected_target": "video",
            "description": "Direct element identification"
        },
        {
            "query": "Where is the main heading shown?",
            "expected_target": "heading",
            "description": "Multi-word label detection"
        },
        {
            "query": "Tell me about the video attributes",
            "expected_target": "video",
            "description": "Attribute-focused query"
        },
        {
            "query": "Show me the button element",
            "expected_target": "button",
            "description": "Show command pattern"
        },
        {
            "query": "Find the navigation bar",
            "expected_target": "navigation",
            "description": "Find command pattern"
        },
        {
            "query": "What are the CSS classes for the heading?",
            "expected_target": "heading",
            "description": "CSS-specific query"
        },
        {
            "query": "XPath for the video element",
            "expected_target": "video",
            "description": "XPath-specific query"
        },
        {
            "query": "video properties and details",
            "expected_target": "video",
            "description": "Properties-focused query"
        }
    ]
    
    print(f"\n🔍 Running {len(test_cases)} intelligent detection tests:")
    print("-" * 60)
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['description']}")
        print(f"   Query: '{test_case['query']}'")
        
        # Test the target label identification
        target_label = processor._identify_target_label(test_case['query'].lower())
        
        print(f"   Detected Target: '{target_label}'")
        print(f"   Expected Target: '{test_case['expected_target']}'")
        
        # Check if the detection is correct
        if target_label == test_case['expected_target']:
            print("   ✅ CORRECT - Target label correctly identified")
            success_count += 1
        elif target_label and test_case['expected_target'] in target_label:
            print("   ✅ PARTIAL - Target label partially identified")
            success_count += 1
        else:
            print("   ❌ INCORRECT - Target label not identified correctly")
        
        # Test the complete workflow with intelligent detection
        print(f"   🔄 Testing complete workflow...")
        
        # Extract keywords using intelligent detection
        keywords = processor._extract_keywords(test_case['query'])
        print(f"   Keywords extracted: {keywords}")
        
        # Search for coordinates using intelligent detection
        coordinate_results = processor._search_coordinates(test_case['query'], 3)
        print(f"   Coordinate matches: {len(coordinate_results)}")
        
        if coordinate_results:
            for result in coordinate_results:
                metadata = result['metadata']
                match_type = result.get('match_type', 'unknown')
                print(f"     - {metadata['label']} (match type: {match_type})")
    
    print(f"\n📊 Test Results Summary:")
    print(f"   Total tests: {len(test_cases)}")
    print(f"   Successful detections: {success_count}")
    print(f"   Success rate: {(success_count/len(test_cases)*100):.1f}%")
    
    # Demonstrate the difference between old and new approach
    print(f"\n🔄 Comparison: Old vs New Approach")
    print("-" * 60)
    
    test_query = "What is the video element on the page?"
    print(f"Query: '{test_query}'")
    
    # New intelligent approach
    target_label = processor._identify_target_label(test_query.lower())
    intelligent_keywords = processor._extract_keywords(test_query)
    
    # Old general approach (simulate)
    general_keywords = processor._extract_general_keywords(test_query.lower())
    
    print(f"\n🧠 Intelligent Approach:")
    print(f"   Target identified: '{target_label}'")
    print(f"   Keywords: {intelligent_keywords}")
    print(f"   Focus: Specific UI element detection")
    
    print(f"\n📝 General Approach:")
    print(f"   Keywords: {general_keywords}")
    print(f"   Focus: All matching vocabulary terms")
    
    print(f"\n🎯 Benefits of Intelligent Detection:")
    print("   ✅ Identifies the specific UI element the user is asking about")
    print("   ✅ Reduces noise by focusing on the target element")
    print("   ✅ Improves accuracy of element retrieval")
    print("   ✅ Provides more relevant responses")
    print("   ✅ Handles various query patterns and phrasings")
    
    # Test with actual coordinate data
    print(f"\n🎯 Real-world Example with Current Data:")
    print("-" * 60)
    
    real_query = "What is the video element on the page?"
    print(f"Query: '{real_query}'")
    
    # Run complete analysis
    result = processor.analyze_query_and_retrieve_context(real_query)
    
    print(f"\n📊 Analysis Results:")
    print(f"   Target detected: {result['keywords']}")
    print(f"   Elements found: {len(result['coordinate_matches'])}")
    print(f"   DOM data retrieved: {len(result['dom_context'])}")
    
    if result['coordinate_matches']:
        print(f"\n📍 Matched Elements:")
        for match in result['coordinate_matches']:
            metadata = match['metadata']
            match_type = match.get('match_type', 'unknown')
            print(f"   - {metadata['label']} at ({metadata['x']}, {metadata['y']}) [{match_type}]")
    
    print(f"\n🎉 Intelligent Label Detection Test Complete!")
    print("   The system successfully identifies specific UI elements")
    print("   that users are asking about from the controlled vocabulary.")

if __name__ == "__main__":
    test_intelligent_label_detection()
