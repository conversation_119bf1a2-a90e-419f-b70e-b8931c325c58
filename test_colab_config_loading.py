#!/usr/bin/env python3
"""
Test script to verify that the Colab notebook loads data from configuration files
instead of using hardcoded sample data.
"""

import json
import os
from pathlib import Path

def test_colab_config_loading():
    """Test that the Colab notebook loads data from configuration file paths"""
    
    print("🧪 Testing Colab Configuration File Loading")
    print("=" * 60)
    
    # Check if the notebook file exists
    notebook_path = "UI_Element_Analyzer_Colab.ipynb"
    if not os.path.exists(notebook_path):
        print(f"❌ Notebook file not found: {notebook_path}")
        return False
    
    # Load the notebook content
    with open(notebook_path, 'r', encoding='utf-8') as f:
        notebook_content = f.read()
    
    # Parse the notebook JSON
    try:
        notebook_data = json.loads(notebook_content)
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing notebook JSON: {e}")
        return False
    
    print(f"✅ Notebook loaded successfully")
    
    # Check for configuration-based data loading
    config_loading_indicators = [
        "load_data_from_config()",
        "config.DEFAULT_COORDINATES_PATH",
        "config.DEFAULT_ELEMENT_INFO_PATH", 
        "config.DEFAULT_LABELS_PATH",
        "_load_controlled_vocabulary",
        "coordinates_data, element_info_data = load_data_from_config()"
    ]
    
    # Check for old hardcoded sample data (should not be present)
    hardcoded_indicators = [
        "sample_coordinates = [",
        "sample_element_info = {",
        "analyzer.initialize_data(sample_coordinates, sample_element_info)"
    ]
    
    print(f"\n🔍 Checking for configuration-based loading...")
    
    config_found = 0
    for indicator in config_loading_indicators:
        if indicator in notebook_content:
            config_found += 1
            print(f"   ✅ Found: {indicator}")
        else:
            print(f"   ⚠️ Missing: {indicator}")
    
    print(f"\n🔍 Checking for hardcoded sample data (should be absent)...")
    
    hardcoded_found = 0
    for indicator in hardcoded_indicators:
        if indicator in notebook_content:
            hardcoded_found += 1
            print(f"   ❌ Found hardcoded data: {indicator}")
        else:
            print(f"   ✅ No hardcoded data: {indicator}")
    
    # Check specific cells for proper implementation
    print(f"\n🔍 Checking specific implementation details...")
    
    # Check for data loading from configuration files
    if "load_data_from_config()" in notebook_content:
        print("   ✅ Data loading function implemented")
    else:
        print("   ❌ Data loading function missing")
    
    # Check for fallback data creation
    if "create_fallback_data()" in notebook_content:
        print("   ✅ Fallback data creation implemented")
    else:
        print("   ❌ Fallback data creation missing")
    
    # Check for controlled vocabulary loading from config
    if "self.config.DEFAULT_LABELS_PATH" in notebook_content:
        print("   ✅ Controlled vocabulary loads from config path")
    else:
        print("   ❌ Controlled vocabulary not loading from config")
    
    # Check for proper initialization
    if "analyzer.initialize_data(coordinates_data, element_info_data)" in notebook_content:
        print("   ✅ Analyzer initialized with loaded data")
    else:
        print("   ❌ Analyzer not properly initialized with loaded data")
    
    # Check for intelligent label detection
    if "_identify_target_label" in notebook_content:
        print("   ✅ Intelligent label detection implemented")
    else:
        print("   ❌ Intelligent label detection missing")
    
    # Check for enhanced coordinate search
    if "_search_by_target_label" in notebook_content:
        print("   ✅ Enhanced coordinate search implemented")
    else:
        print("   ❌ Enhanced coordinate search missing")
    
    print(f"\n📊 Summary:")
    print(f"   Configuration loading indicators found: {config_found}/{len(config_loading_indicators)}")
    print(f"   Hardcoded data indicators found: {hardcoded_found}/{len(hardcoded_indicators)}")
    
    # Determine success
    success = (
        config_found >= len(config_loading_indicators) - 1 and  # Allow for 1 missing
        hardcoded_found == 0  # No hardcoded data should be present
    )
    
    if success:
        print(f"\n🎉 SUCCESS: Colab notebook properly loads data from configuration files!")
        print(f"   ✅ Configuration-based loading: {config_found}/{len(config_loading_indicators)}")
        print(f"   ✅ No hardcoded sample data: {hardcoded_found}/{len(hardcoded_indicators)}")
        print(f"   ✅ Intelligent label detection implemented")
        print(f"   ✅ Enhanced coordinate search implemented")
    else:
        print(f"\n❌ FAILURE: Colab notebook still uses hardcoded data or missing config loading")
        print(f"   Configuration indicators: {config_found}/{len(config_loading_indicators)}")
        print(f"   Hardcoded indicators: {hardcoded_found}/{len(hardcoded_indicators)}")
    
    # Check for key features
    print(f"\n🎯 Key Features Verification:")
    
    features = {
        "Data Loading from Config Files": "load_data_from_config()" in notebook_content,
        "Controlled Vocabulary from Config": "self.config.DEFAULT_LABELS_PATH" in notebook_content,
        "Intelligent Label Detection": "_identify_target_label" in notebook_content,
        "Enhanced Coordinate Search": "_search_by_target_label" in notebook_content,
        "Fallback Data Support": "create_fallback_data()" in notebook_content,
        "File Upload Interface": "upload_custom_data()" in notebook_content,
        "Interactive Query Interface": "create_query_interface()" in notebook_content
    }
    
    for feature, implemented in features.items():
        status = "✅" if implemented else "❌"
        print(f"   {status} {feature}")
    
    implemented_count = sum(features.values())
    print(f"\n📈 Implementation Status: {implemented_count}/{len(features)} features implemented")
    
    return success

if __name__ == "__main__":
    success = test_colab_config_loading()
    if success:
        print(f"\n🚀 The Colab notebook is ready for production use!")
        print(f"   📁 Loads data from configuration file paths")
        print(f"   🎯 Implements intelligent label detection")
        print(f"   🔍 Uses controlled vocabulary from label.txt")
        print(f"   📊 Provides fallback data when files unavailable")
    else:
        print(f"\n⚠️ The Colab notebook needs additional updates.")
