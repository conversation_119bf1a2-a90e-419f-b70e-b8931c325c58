#!/usr/bin/env python3
"""
Test script to demonstrate the controlled vocabulary functionality
for keyword extraction in UI element analysis.
"""

import json
from ui_analyzer import UIElementProcessor

def test_controlled_vocabulary():
    """Test the controlled vocabulary keyword extraction functionality"""
    
    print("🧪 Testing Controlled Vocabulary Functionality")
    print("=" * 60)
    
    # Initialize processor
    processor = UIElementProcessor()
    
    # Load sample data
    processor.process_and_store_data("coordinates.json", "element_info.json")
    
    print(f"\n📊 Controlled Vocabulary Stats:")
    print(f"   Total terms loaded: {len(processor.controlled_vocabulary)}")
    print(f"   Sample terms: {list(processor.controlled_vocabulary)[:10]}")
    
    # Test cases for controlled vocabulary
    test_cases = [
        {
            "query": "What is the video element on the page?",
            "expected_keywords": ["video", "page"],
            "description": "Basic vocabulary matching"
        },
        {
            "query": "Where is the main heading shown?",
            "expected_keywords": ["main", "heading"],
            "description": "Multi-word label matching"
        },
        {
            "query": "Show me the button and icon elements",
            "expected_keywords": ["button", "and", "icon"],
            "description": "Multiple vocabulary matches"
        },
        {
            "query": "Find the navigation bar on the page",
            "expected_keywords": ["navigation", "page"],
            "description": "Partial vocabulary matching"
        },
        {
            "query": "What are some random words not in vocabulary?",
            "expected_keywords": [],
            "description": "No vocabulary matches"
        },
        {
            "query": "Show me the vidoe element",  # Typo
            "expected_keywords": ["video"],
            "description": "Fuzzy matching for typos"
        }
    ]
    
    print(f"\n🔍 Running {len(test_cases)} test cases:")
    print("-" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['description']}")
        print(f"   Query: '{test_case['query']}'")
        
        # Extract keywords using controlled vocabulary
        keywords = processor._extract_keywords(test_case['query'])
        
        print(f"   Extracted: {keywords}")
        print(f"   Expected: {test_case['expected_keywords']}")
        
        # Check if results match expectations
        if set(keywords) == set(test_case['expected_keywords']):
            print("   ✅ PASS")
        else:
            print("   ❌ FAIL")
    
    print(f"\n🎯 Controlled Vocabulary Benefits Demonstrated:")
    print("   ✅ Only extracts keywords that match predefined UI element labels")
    print("   ✅ Eliminates irrelevant words from keyword extraction")
    print("   ✅ Handles multi-word labels and individual words")
    print("   ✅ Provides fuzzy matching for typos and variations")
    print("   ✅ Ensures precise mapping between queries and UI elements")
    
    # Test the complete workflow with controlled vocabulary
    print(f"\n🔄 Testing Complete Workflow with Controlled Vocabulary:")
    print("-" * 60)
    
    test_query = "What is the video element on the page?"
    print(f"Query: '{test_query}'")
    
    # Run the complete analysis
    result = processor.analyze_query_and_retrieve_context(test_query)
    
    print(f"\n📊 Workflow Results:")
    print(f"   Keywords from controlled vocabulary: {result['keywords']}")
    print(f"   Coordinate matches found: {len(result['coordinate_matches'])}")
    print(f"   DOM elements retrieved: {len(result['dom_context'])}")
    print(f"   Relevant indices: {result['relevant_indices']}")
    
    # Show context snippet
    if result['combined_context']:
        context_lines = result['combined_context'].split('\n')[:5]
        print(f"\n📝 Context Preview:")
        for line in context_lines:
            if line.strip():
                print(f"   {line.strip()}")
    
    print(f"\n🎉 Controlled Vocabulary Test Complete!")
    print("   The system successfully uses label.txt to ensure accurate keyword extraction")
    print("   and reliable mapping between user queries and UI elements.")

if __name__ == "__main__":
    test_controlled_vocabulary()
