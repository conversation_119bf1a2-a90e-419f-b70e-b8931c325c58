"""
Test script to verify the vector database functionality without requiring Gemini API
"""
import json
from typing import Dict, List, Any
import chromadb
from sentence_transformers import SentenceTransformer
from config import Config

class UIElementProcessor:
    """Handles processing and storage of UI elements in vector database"""
    
    def __init__(self):
        self.config = Config()
        # Set Hugging Face token for model downloads
        import os
        os.environ["HUGGINGFACE_HUB_TOKEN"] = self.config.HUGGINGFACE_TOKEN
        
        try:
            self.embeddings = SentenceTransformer(
                self.config.EMBEDDING_MODEL,
                token=self.config.HUGGINGFACE_TOKEN
            )
            print("✅ SentenceTransformer loaded successfully!")
        except Exception as e:
            print(f"❌ Could not load SentenceTransformer: {e}")
            self.embeddings = None
            
        self.chroma_client = chromadb.PersistentClient(path=self.config.CHROMA_DB_PATH)
        self.collection = None
        self._initialize_collection()
    
    def _initialize_collection(self):
        """Initialize or get existing ChromaDB collection"""
        try:
            self.collection = self.chroma_client.get_collection(name=self.config.COLLECTION_NAME)
            print(f"✅ Loaded existing collection: {self.config.COLLECTION_NAME}")
        except:
            self.collection = self.chroma_client.create_collection(
                name=self.config.COLLECTION_NAME,
                metadata={"description": "UI elements with coordinates and DOM data"}
            )
            print(f"✅ Created new collection: {self.config.COLLECTION_NAME}")
    
    def process_and_store_data(self, coordinates_path: str, element_info_path: str):
        """Process and store UI element data in vector database"""
        # Load coordinates data
        with open(coordinates_path, 'r') as f:
            coordinates_data = json.load(f)
        
        # Load element info data
        with open(element_info_path, 'r') as f:
            element_info_data = json.load(f)
        
        # Clear existing data
        try:
            self.collection.delete()
        except:
            pass
        
        documents = []
        metadatas = []
        ids = []
        
        for i, coord_item in enumerate(coordinates_data):
            element_key = f"element_{i+1}"
            
            if element_key in element_info_data:
                element_info = element_info_data[element_key]
                
                # Create document text for embedding
                doc_text = self._create_document_text(coord_item, element_info)
                
                # Create metadata
                metadata = {
                    "element_id": element_key,
                    "label": coord_item["label"],
                    "x": coord_item["coordinates"]["x"],
                    "y": coord_item["coordinates"]["y"],
                    "width": coord_item["coordinates"]["width"],
                    "height": coord_item["coordinates"]["height"],
                    "tag": element_info.get("tag", ""),
                    "text": element_info.get("text", ""),
                    "css_selector": element_info.get("cssSelector", ""),
                    "xpath": element_info.get("xpath", "")
                }
                
                documents.append(doc_text)
                metadatas.append(metadata)
                ids.append(element_key)
        
        # Store in ChromaDB
        if documents:
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            print(f"✅ Stored {len(documents)} UI elements in vector database")
    
    def _create_document_text(self, coord_item: Dict, element_info: Dict) -> str:
        """Create searchable text from UI element data"""
        text_parts = [
            f"Label: {coord_item['label']}",
            f"Tag: {element_info.get('tag', '')}",
            f"Text content: {element_info.get('text', '')}",
            f"CSS classes: {' '.join(element_info.get('classes', []))}",
            f"Position: x={coord_item['coordinates']['x']}, y={coord_item['coordinates']['y']}",
            f"Size: {coord_item['coordinates']['width']}x{coord_item['coordinates']['height']}",
        ]
        
        # Add attributes if available
        if 'attributes' in element_info:
            attrs = element_info['attributes']
            if attrs:
                attr_text = ", ".join([f"{k}={v}" for k, v in attrs.items() if v])
                text_parts.append(f"Attributes: {attr_text}")
        
        return " | ".join(text_parts)
    
    def search_relevant_elements(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """Search for relevant UI elements based on query"""
        if top_k is None:
            top_k = self.config.TOP_K_RESULTS
        
        if self.embeddings is not None:
            # Use vector search if embeddings are available
            results = self.collection.query(
                query_texts=[query],
                n_results=top_k
            )
            
            relevant_elements = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i]
                    relevant_elements.append({
                        "document": doc,
                        "metadata": metadata,
                        "distance": results['distances'][0][i] if 'distances' in results else None
                    })
        else:
            # Fallback to simple text matching
            all_items = self.collection.get()
            relevant_elements = []
            
            query_lower = query.lower()
            for i, doc in enumerate(all_items['documents']):
                metadata = all_items['metadatas'][i]
                # Simple keyword matching
                if any(keyword in doc.lower() for keyword in query_lower.split()):
                    relevant_elements.append({
                        "document": doc,
                        "metadata": metadata,
                        "distance": 0.5  # Default similarity score
                    })
            
            # Limit results
            relevant_elements = relevant_elements[:top_k]
        
        return relevant_elements

def test_vector_search():
    """Test the vector database functionality"""
    print("\n" + "="*60)
    print("🧪 TESTING VECTOR DATABASE FUNCTIONALITY")
    print("="*60)
    
    # Initialize processor
    processor = UIElementProcessor()
    
    # Load data
    processor.process_and_store_data("coordinates.json", "element_info.json")
    
    # Test queries
    test_queries = [
        "main heading",
        "video element", 
        "figure",
        "text content",
        "button"
    ]
    
    print(f"\n📋 Testing {len(test_queries)} queries:")
    print("-" * 40)
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        results = processor.search_relevant_elements(query)
        
        if results:
            print(f"   Found {len(results)} relevant elements:")
            for i, result in enumerate(results, 1):
                metadata = result["metadata"]
                distance = result.get("distance", "N/A")
                print(f"   {i}. {metadata['label']} - Tag: {metadata['tag']} - Distance: {distance:.3f}")
                print(f"      Position: ({metadata['x']}, {metadata['y']}) - Size: {metadata['width']}x{metadata['height']}")
                if metadata['text']:
                    print(f"      Text: '{metadata['text'][:50]}{'...' if len(metadata['text']) > 50 else ''}'")
        else:
            print("   ❌ No relevant elements found")
    
    print(f"\n✅ Vector database test completed!")
    print("="*60)

if __name__ == "__main__":
    test_vector_search()
