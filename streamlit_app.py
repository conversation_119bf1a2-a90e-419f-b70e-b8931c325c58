import streamlit as st
import json
import os
from PIL import Image
import tempfile
from ui_analyzer import UIAnalyzer
from config import Config

# Page configuration
st.set_page_config(
    page_title="UI Element Analyzer",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'analyzer' not in st.session_state:
    st.session_state.analyzer = None
if 'data_initialized' not in st.session_state:
    st.session_state.data_initialized = False
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []

def initialize_analyzer():
    """Initialize the UI analyzer"""
    try:
        st.session_state.analyzer = UIAnalyzer()
        return True
    except Exception as e:
        st.error(f"Failed to initialize analyzer: {str(e)}")
        return False

def load_data_files(screenshot_file, coordinates_file, element_info_file):
    """Load and process uploaded files"""
    try:
        # Save uploaded files temporarily
        temp_dir = tempfile.mkdtemp()

        # Save screenshot
        screenshot_path = os.path.join(temp_dir, "screenshot.png")
        with open(screenshot_path, "wb") as f:
            f.write(screenshot_file.getvalue())

        # Save coordinates JSON
        coordinates_path = os.path.join(temp_dir, "coordinates.json")
        coordinates_data = json.loads(coordinates_file.getvalue().decode())
        with open(coordinates_path, "w") as f:
            json.dump(coordinates_data, f)

        # Save element info JSON
        element_info_path = os.path.join(temp_dir, "element_info.json")
        element_info_data = json.loads(element_info_file.getvalue().decode())
        with open(element_info_path, "w") as f:
            json.dump(element_info_data, f)

        # Initialize analyzer with uploaded data
        st.session_state.analyzer.initialize_data(coordinates_path, element_info_path)
        st.session_state.data_initialized = True
        st.session_state.screenshot_path = screenshot_path

        return True, "Data loaded successfully!"

    except Exception as e:
        return False, f"Error loading data: {str(e)}"

def main():
    st.title("🔍 UI Element Analyzer")
    st.markdown("Analyze UI elements using LangChain, LangGraph, and Gemini AI")

    # Sidebar for file uploads and configuration
    with st.sidebar:
        st.header("📁 Data Upload")

        # Check if Google API key is set
        config = Config()
        if not config.GOOGLE_API_KEY:
            st.error("⚠️ Please set your GOOGLE_API_KEY in the .env file")
            st.stop()

        # Initialize analyzer
        if st.session_state.analyzer is None:
            with st.spinner("Initializing analyzer..."):
                if not initialize_analyzer():
                    st.stop()

        # File upload section
        st.subheader("Upload Files")

        # Option to use default files or upload new ones
        use_default = st.checkbox("Use default files", value=True)

        if use_default:
            # Use default files
            if st.button("Load Default Data"):
                try:
                    st.session_state.analyzer.initialize_data()
                    st.session_state.data_initialized = True
                    st.session_state.screenshot_path = config.DEFAULT_SCREENSHOT_PATH
                    st.success("Default data loaded successfully!")
                except Exception as e:
                    st.error(f"Error loading default data: {str(e)}")
        else:
            # Upload custom files
            screenshot_file = st.file_uploader(
                "Upload Screenshot",
                type=['png', 'jpg', 'jpeg'],
                help="Upload the screenshot of the web page"
            )

            coordinates_file = st.file_uploader(
                "Upload Coordinates JSON",
                type=['json'],
                help="Upload the JSON file containing UI element coordinates and labels"
            )

            element_info_file = st.file_uploader(
                "Upload Element Info JSON",
                type=['json'],
                help="Upload the JSON file containing DOM-related data"
            )

            if screenshot_file and coordinates_file and element_info_file:
                if st.button("Process Uploaded Files"):
                    with st.spinner("Processing files..."):
                        success, message = load_data_files(
                            screenshot_file, coordinates_file, element_info_file
                        )
                        if success:
                            st.success(message)
                        else:
                            st.error(message)

        # Display data status
        st.subheader("📊 Status")
        if st.session_state.data_initialized:
            st.success("✅ Data initialized")
        else:
            st.warning("⏳ Please load data first")

    # Main content area
    if st.session_state.data_initialized:
        # Display screenshot if available
        col1, col2 = st.columns([1, 1])

        with col1:
            st.subheader("📷 Screenshot")
            try:
                if hasattr(st.session_state, 'screenshot_path'):
                    image = Image.open(st.session_state.screenshot_path)
                    st.image(image, caption="Web Page Screenshot", use_column_width=True)
                else:
                    # Try default screenshot
                    image = Image.open(config.DEFAULT_SCREENSHOT_PATH)
                    st.image(image, caption="Web Page Screenshot", use_column_width=True)
            except Exception as e:
                st.error(f"Could not load screenshot: {str(e)}")

        with col2:
            st.subheader("💬 Chat Interface")

            # Chat history
            chat_container = st.container()
            with chat_container:
                for i, (query, response) in enumerate(st.session_state.chat_history):
                    with st.chat_message("user"):
                        st.write(query)
                    with st.chat_message("assistant"):
                        st.write(response)

            # Query input
            st.subheader("Ask about the UI")

            # Predefined example queries
            example_queries = [
                "Where is the main heading shown on the page?",
                "What is the video element on the page?",
                "Tell me about the figure element and its location",
                "What elements are in the header area?",
                "Where can I find interactive elements?"
            ]

            selected_example = st.selectbox(
                "Choose an example query:",
                [""] + example_queries,
                index=0
            )

            # Text input for custom query
            user_query = st.text_area(
                "Or enter your own question:",
                value=selected_example,
                height=100,
                placeholder="Ask about UI elements, their locations, functions, etc."
            )

            if st.button("🔍 Analyze", type="primary"):
                if user_query.strip():
                    with st.spinner("Analyzing..."):
                        try:
                            response = st.session_state.analyzer.analyze_query(user_query)

                            # Add to chat history
                            st.session_state.chat_history.append((user_query, response))

                            # Display response
                            with st.chat_message("user"):
                                st.write(user_query)
                            with st.chat_message("assistant"):
                                st.write(response)

                        except Exception as e:
                            st.error(f"Error analyzing query: {str(e)}")
                else:
                    st.warning("Please enter a question")

            # Clear chat history
            if st.button("🗑️ Clear Chat History"):
                st.session_state.chat_history = []
                st.rerun()

        # Additional information
        with st.expander("ℹ️ How to use"):
            st.markdown("""
            ### How to use the UI Element Analyzer:

            1. **Load Data**: Either use the default files or upload your own:
               - Screenshot: Image of the web page
               - Coordinates JSON: UI element positions and labels
               - Element Info JSON: DOM data for each element

            2. **Ask Questions**: You can ask about:
               - Element locations: "Where is the brand logo?"
               - Element functions: "What does the search button do?"
               - Element properties: "What are the dimensions of the video?"
               - General UI questions: "What interactive elements are available?"

            3. **Get Detailed Responses**: The AI will provide:
               - Specific coordinate information
               - Element descriptions and functions
               - Context from the screenshot and DOM data

            ### Example Questions:
            - "Where is the main heading located?"
            - "What video content is shown on the page?"
            - "Tell me about clickable elements in the header"
            - "What is the size and position of the figure element?"
            """)

    else:
        st.info("👆 Please load data using the sidebar to start analyzing UI elements")

if __name__ == "__main__":
    main()
